"""
Data management module for Ultratrader optimization system.

Handles historical data fetching, caching, and preprocessing with intelligent
retry mechanisms and rate limiting.
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta, UTC
from pathlib import Path
from typing import Optional, List
from binance import AsyncClient
from binance.exceptions import BinanceAPIException

from .config import config


class DataManager:
    """
    Manages historical data fetching and caching for cryptocurrency trading.
    
    Features:
    - Intelligent caching with expiration
    - Rate limiting and retry mechanisms
    - Chunked data fetching for large datasets
    - Efficient data preprocessing
    """
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.cache_dir = config.data.cache_dir
        self.cache_dir.mkdir(exist_ok=True)
    
    async def get_historical_data(
        self, 
        symbol: str, 
        timeframe: str, 
        history_days: int, 
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Enhanced data fetching with intelligent chunking and caching.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            timeframe: Kline interval (e.g., '15m', '1h')
            history_days: Number of days of historical data
            end_date: Optional end date for historical data
            
        Returns:
            DataFrame with OHLCV data and timestamp index
        """
        cache_suffix = f"_{end_date}" if end_date else ""
        cache_file = self.cache_dir / f"{symbol}_{timeframe}_{history_days}d{cache_suffix}.feather"
        
        # Check cache validity
        if self._is_cache_valid(cache_file):
            print(f"Loading cached data for {symbol} ({timeframe})...")
            df = pd.read_feather(cache_file)
            df = df.set_index('timestamp')
            return df

        print(f"Fetching fresh data for {symbol} ({timeframe})...")
        
        # Fetch fresh data
        df = await self._fetch_fresh_data(symbol, timeframe, history_days, end_date)
        
        if not df.empty:
            # Cache the data
            df_to_cache = df.reset_index()
            df_to_cache.to_feather(cache_file)
            print(f"Data for {symbol} ({timeframe}) cached successfully.")
        
        return df
    
    def _is_cache_valid(self, cache_file: Path) -> bool:
        """Check if cache file exists and is not expired."""
        if not cache_file.exists():
            return False
        
        file_mod_time = datetime.fromtimestamp(cache_file.stat().st_mtime, tz=UTC)
        expiration_time = timedelta(hours=config.data.cache_expiration_hours)
        
        return (datetime.now(UTC) - file_mod_time) < expiration_time
    
    async def _fetch_fresh_data(
        self, 
        symbol: str, 
        timeframe: str, 
        history_days: int, 
        end_date: Optional[str] = None
    ) -> pd.DataFrame:
        """Fetch fresh data from Binance - restored to original working version."""
        # Calculate expected data volume
        minutes_per_bar = self._get_minutes_per_bar(timeframe)
        total_minutes = history_days * 24 * 60
        expected_rows = total_minutes // minutes_per_bar
        
        print(f"Fetching fresh data for {symbol} ({timeframe})...")
        
        end_time = (datetime.now(UTC) if end_date is None 
                   else datetime.fromisoformat(end_date).replace(tzinfo=UTC))
        start_time = end_time - timedelta(days=history_days)
        
        all_data = []
        chunk_size = 1500  # Restored original Binance limit
        current_end = end_time
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                while len(all_data) < expected_rows and current_end > start_time:
                    klines = await self.client.futures_klines(
                        symbol=symbol, 
                        interval=timeframe,
                        startTime=int(start_time.timestamp() * 1000),
                        endTime=int(current_end.timestamp() * 1000),
                        limit=chunk_size
                    )
                    
                    if not klines:
                        break
                        
                    chunk_df = pd.DataFrame(klines, columns=[
                        'open_time', 'open', 'high', 'low', 'close', 'volume',
                        'close_time', 'quote_asset_volume', 'number_of_trades',
                        'taker_buy_base', 'taker_buy_quote', 'ignore'
                    ])
                    
                    all_data = chunk_df.values.tolist() + all_data
                    current_end = (datetime.fromtimestamp(klines[0][0] / 1000, tz=UTC) 
                                 - timedelta(milliseconds=1))
                    
                    if len(all_data) >= expected_rows:
                        break
                        
                    # Rate limiting
                    await asyncio.sleep(0.1)
                
                break  # Success
                
            except BinanceAPIException as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"Binance API error for {symbol}: {e}. Retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"Failed to fetch data for {symbol} after {max_retries} attempts")
                    return pd.DataFrame()
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"Error fetching data for {symbol}: {e}. Retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                else:
                    print(f"Failed to fetch data for {symbol} after {max_retries} attempts")
                    return pd.DataFrame()
        
        return self._process_raw_data(all_data) if all_data else pd.DataFrame()
    
    def _get_minutes_per_bar(self, timeframe: str) -> int:
        """Convert timeframe string to minutes per bar."""
        if timeframe.endswith('m'):
            return int(timeframe[:-1])
        elif timeframe.endswith('h'):
            return int(timeframe[:-1]) * 60
        elif timeframe.endswith('d'):
            return int(timeframe[:-1]) * 24 * 60
        else:
            # Default to 15 minutes if unknown
            return 15
    
    def _process_raw_data(self, raw_data: List) -> pd.DataFrame:
        """Process raw kline data into clean DataFrame."""
        if not raw_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(raw_data, columns=[
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base', 'taker_buy_quote', 'ignore'
        ])
        
        # Clean and prepare
        df = df.drop_duplicates(subset=['open_time'])
        df = df.sort_values('open_time')
        
        # Convert price and volume columns to float
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)
        
        # Create timestamp index
        df['timestamp'] = pd.to_datetime(df['open_time'], unit='ms', utc=True)
        df = df.set_index('timestamp')
        
        return df
    
    async def get_multiple_timeframes(
        self, 
        symbol: str, 
        timeframes: List[str], 
        history_days: int
    ) -> dict[str, pd.DataFrame]:
        """
        Fetch data for multiple timeframes concurrently.
        
        Args:
            symbol: Trading symbol
            timeframes: List of timeframe strings
            history_days: Days of historical data
            
        Returns:
            Dictionary mapping timeframes to DataFrames
        """
        tasks = [
            self.get_historical_data(symbol, tf, history_days) 
            for tf in timeframes
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            tf: result if isinstance(result, pd.DataFrame) else pd.DataFrame()
            for tf, result in zip(timeframes, results)
        }
    
    def clean_cache(self, max_age_days: int = 7):
        """Remove old cache files to free up space."""
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        
        removed_count = 0
        for cache_file in self.cache_dir.glob("*.feather"):
            file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
            if file_time < cutoff_time:
                cache_file.unlink()
                removed_count += 1
        
        if removed_count > 0:
            print(f"Removed {removed_count} old cache files")
    
    def get_cache_info(self) -> dict:
        """Get information about cached data."""
        cache_files = list(self.cache_dir.glob("*.feather"))
        total_size = sum(f.stat().st_size for f in cache_files)
        
        return {
            'file_count': len(cache_files),
            'total_size_mb': total_size / (1024 * 1024),
            'files': [f.name for f in cache_files]
        } 