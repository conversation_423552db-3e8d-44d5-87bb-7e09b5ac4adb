"""
Configuration module for Ultratrader optimization system.

Centralizes all configuration parameters with validation and environment support.
"""

import os
from pathlib import Path
from dataclasses import dataclass, field
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class MarketConfig:
    """Market and symbol configuration."""
    symbol_quote: str = 'USDT'
    timeframe: str = '15m'
    mta_timeframe: str = '1h'
    target_symbol: Optional[str] = None  # None for auto-selection of top symbols for diversification
    top_n_symbols: int = 5  # Increase to 5 symbols for better diversification and opportunities


@dataclass
class DataConfig:
    """Data fetching and caching configuration."""
    history_days: int = 180  # Expanded to 6 months for robust validation
    validation_days: int = 30  # Increased validation period
    cache_expiration_hours: int = 6
    max_concurrent_requests: int = 3
    
    # Cache and output directories
    cache_dir: Path = field(default_factory=lambda: Path("data_cache"))
    charts_dir: Path = field(default_factory=lambda: Path("charts"))
    validation_charts_dir: Path = field(default_factory=lambda: Path("validation_charts"))


@dataclass
class TradingConfig:
    """Trading and risk management configuration."""
    # Core trading settings - EXTREME for crypto microscopic movement overcoming
    risk_per_trade: float = 0.15  # 15% risk per trade (extreme but needed for crypto)
    initial_balance: float = 1000.0  
    futures_taker_fee: float = 0.0008  # Higher fees to be more realistic
    max_drawdown_limit: float = 0.50  # 50% maximum drawdown (crypto reality)
    
    # Enhanced risk controls - crypto-optimized
    max_consecutive_losses: int = 6  # More tolerance for crypto volatility
    daily_loss_limit: float = 0.10  # 10% daily loss limit (crypto volatility)
    min_win_rate: float = 0.25  # Lower win rate acceptable with good RR
    max_position_size_pct: float = 0.15  # Max 15% of balance per trade (crypto sizing)


@dataclass
class OptimizationConfig:
    """Optimization algorithm configuration."""
    # Increased trials for better parameter exploration to reach 5% target
    optuna_trials_trend: int = 75       # Increased for better exploration of aggressive parameters
    optuna_trials_reversion: int = 75   # Increased for better exploration of aggressive parameters
    optuna_trials_adx: int = 50         # Increased for better exploration of aggressive parameters
    
    # Extended timeouts for thorough aggressive optimization
    optuna_timeout_per_phase: int = 300  # 5 minutes max per optimization
    optuna_timeout_per_trial: int = 45   # 45 seconds max per trial
    
    # Realistic crypto validation criteria  
    min_trades: int = 1  # Accept any strategy with trades
    min_sortino_ratio: float = 0.5  # Reasonable for crypto
    min_profit_factor: float = 0.8  # Near break-even acceptable
    max_drawdown: float = 0.50  # High drawdown tolerance for crypto
    min_win_rate: float = 0.15  # 15% minimum realistic for crypto
    
    # Walk-forward validation parameters - crypto-realistic
    min_walk_forward_windows: int = 1  # Accept single validation
    stability_threshold: float = -10.0   # Allow some instability
    consistency_threshold: float = 0.2   # 20% consistency acceptable
    
    # Scoring weights - emphasize returns over stability for 5% target
    quantum_score_weight: float = 0.6  # Higher weight on quantum score
    walk_forward_weight: float = 0.2   # Lower weight on walk-forward stability
    robustness_weight: float = 0.2     # Lower weight on robustness


@dataclass
class MarketFilterConfig:
    """Market condition filtering configuration."""
    # Volatility filters - aggressive for more opportunities
    min_volatility_percentile: float = 5.0   # Very low minimum - trade in almost all conditions  
    max_volatility_percentile: float = 98.0  # Very high maximum - trade in high volatility
    
    # Trend filters - very lenient for more trades
    trend_strength_threshold: float = 0.1    # Very low trend requirement
    adx_min_threshold: float = 10.0          # Very low ADX requirement
    adx_max_threshold: float = 80.0          # Allow very strong trends
    
    # Volume filters - aggressive for more opportunities
    volume_spike_threshold: float = 1.2      # 20% above average volume
    volume_drought_threshold: float = 0.6    # 40% below average volume (still allow)
    
    # Market timing filters - NEW aggressive approach
    market_session_enabled: bool = True      # Enable session-based filtering
    optimal_trading_hours: list = None       # UTC hours - will be set to [6,22] for Asian+European+US overlap
    avoid_weekend_gaps: bool = True          # Avoid trading near weekend close/open
    
    # Momentum filters - very aggressive
    momentum_lookback_periods: int = 24      # 6 hours lookback (24 x 15min bars)
    momentum_threshold: float = 0.002        # 0.2% minimum momentum to enter
    
    # Market regime detection - aggressive for trend-following
    regime_detection_enabled: bool = True    # Enable market regime detection
    trending_market_score_threshold: float = 0.3  # Low threshold for trending markets
    ranging_market_score_threshold: float = 0.7   # High threshold for ranging markets


@dataclass
class OutputConfig:
    """Output and scoring configuration."""
    final_scoring_method: str = 'Quantum'  # 'Quantum' or 'Sortino'
    max_strategies_to_save: int = 5


class Config:
    """Main configuration class that combines all config sections."""
    
    def __init__(self):
        self.market = MarketConfig()
        self.data = DataConfig()
        self.trading = TradingConfig()
        self.optimization = OptimizationConfig()
        self.market_filters = MarketFilterConfig()
        self.output = OutputConfig()
        
        # Initialize optimal trading hours for maximum market overlap
        if self.market_filters.optimal_trading_hours is None:
            self.market_filters.optimal_trading_hours = list(range(6, 22))  # 6 AM to 10 PM UTC
        
        # Ensure directories exist
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        self.data.cache_dir.mkdir(exist_ok=True)
        self.data.charts_dir.mkdir(exist_ok=True)
        self.data.validation_charts_dir.mkdir(exist_ok=True)
    
    def get_binance_credentials(self) -> tuple[str, str]:
        """Get Binance API credentials from environment variables."""
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_SECRET_KEY')
        
        if not api_key or not api_secret:
            raise ValueError(
                "Binance API credentials not found. "
                "Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables."
            )
        
        return api_key, api_secret
    
    def validate(self):
        """Validate configuration parameters."""
        # Market validation
        if self.market.timeframe not in ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d']:
            raise ValueError(f"Invalid timeframe: {self.market.timeframe}")
        
        # Trading validation - Updated for aggressive parameters
        if not 0 < self.trading.risk_per_trade <= 0.15:  # Allow up to 15% risk for aggressive strategies
            raise ValueError("Risk per trade must be between 0 and 15%")
        
        if not 0 < self.trading.max_drawdown_limit <= 1.0:
            raise ValueError("Max drawdown limit must be between 0 and 100%")
        
        # Data validation
        if self.data.history_days < self.data.validation_days:
            raise ValueError("History days must be greater than validation days")
            
        # Optimization validation - ensure minimum requirements
        if self.optimization.min_trades < 1:
            raise ValueError("Minimum trades must be at least 1")
            
        if self.optimization.optuna_trials_trend < 5:
            raise ValueError("Optuna trials must be at least 5 for meaningful optimization")


# Global configuration instance
config = Config()


def update_config_from_env():
    """Update configuration from environment variables if available."""
    # Market settings
    if symbol := os.getenv('TARGET_SYMBOL'):
        config.market.target_symbol = symbol
    
    if timeframe := os.getenv('TIMEFRAME'):
        config.market.timeframe = timeframe
    
    # Trading settings
    if risk := os.getenv('RISK_PER_TRADE'):
        config.trading.risk_per_trade = float(risk)
    
    if balance := os.getenv('INITIAL_BALANCE'):
        config.trading.initial_balance = float(balance)
    
    # Data settings
    if days := os.getenv('HISTORY_DAYS'):
        config.data.history_days = int(days)


# Auto-update from environment on import
update_config_from_env()
config.validate() 