#!/usr/bin/env python3
"""
Ultratrader Optimization System - Main Entry Point

A modular, high-performance cryptocurrency trading strategy optimization system
with advanced risk management and comprehensive backtesting capabilities.

Usage:
    python main.py [options]
"""

import os
import sys
import asyncio
import logging
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Windows compatibility - must be set before any asyncio operations
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Core imports
from core.config import config
from core.data_manager import DataManager
from core.backtest_engine import BacktestEngine

# Utilities and modules
from utils.indicators import IndicatorCalculator
from utils.scoring import ScoringSystem
from risk.risk_manager import RiskManager
from optimization.optimizer import OptunaOptimizer, WalkForwardAnalyzer

# External dependencies
from binance import AsyncClient
from binance.exceptions import BinanceAPIException
import pandas as pd
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ultratrader_modular.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class UltraderOrchestrator:
    """
    Main orchestrator for the Ultratrader optimization system.
    
    Coordinates all components to execute the complete optimization pipeline:
    1. Data acquisition and preprocessing
    2. Strategy optimization across multiple modes
    3. Walk-forward validation
    4. Performance evaluation and ranking
    5. Results saving and reporting
    """
    
    def __init__(self):
        self.client: Optional[AsyncClient] = None
        self.data_manager: Optional[DataManager] = None
        self.backtest_engine = BacktestEngine()
        self.risk_manager = RiskManager()
        self.optimizer = OptunaOptimizer(self.backtest_engine)
        self.walk_forward_analyzer = WalkForwardAnalyzer(self.optimizer)
        
        logger.info("Ultratrader Orchestrator initialized")
    
    async def initialize_binance_client(self):
        """Initialize Binance API client."""
        try:
            api_key, api_secret = config.get_binance_credentials()
            self.client = await AsyncClient.create(api_key, api_secret)
            self.data_manager = DataManager(self.client)
            logger.info("Binance client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Binance client: {e}")
            raise
    
    async def get_market_data(self, symbol: str) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Fetch and prepare market data for optimization.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            
        Returns:
            Tuple of (main_timeframe_data, mta_timeframe_data)
        """
        logger.info(f"Fetching market data for {symbol}")
        
        try:
            # Fetch main timeframe data
            main_df = await self.data_manager.get_historical_data(
                symbol=symbol,
                timeframe=config.market.timeframe,
                history_days=config.data.history_days
            )
            
            # Fetch MTA timeframe data
            mta_df = await self.data_manager.get_historical_data(
                symbol=symbol,
                timeframe=config.market.mta_timeframe,
                history_days=config.data.history_days
            )
            
            if main_df.empty:
                raise ValueError(f"No data retrieved for {symbol}")
            
            logger.info(f"Retrieved {len(main_df)} bars for {symbol} ({config.market.timeframe})")
            logger.info(f"Retrieved {len(mta_df)} bars for {symbol} ({config.market.mta_timeframe})")
            
            return main_df, mta_df
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            raise
    
    async def optimize_single_strategy(
        self,
        symbol: str,
        strategy_mode: str,
        main_df: pd.DataFrame,
        mta_df: pd.DataFrame
    ) -> Optional[Dict[str, Any]]:
        """
        Optimize a single strategy for a given symbol and mode.
        
        Args:
            symbol: Trading symbol
            strategy_mode: Strategy mode ('Trend', 'Reversion', 'Adaptive')
            main_df: Main timeframe data
            mta_df: MTA timeframe data
            
        Returns:
            Optimization results or None if failed
        """
        logger.info(f"Optimizing {strategy_mode} strategy for {symbol}")
        
        try:
            # Determine trial count based on strategy mode
            if strategy_mode == 'Trend':
                n_trials = config.optimization.optuna_trials_trend
            elif strategy_mode == 'Reversion':
                n_trials = config.optimization.optuna_trials_reversion
            elif strategy_mode == 'Adaptive':
                n_trials = config.optimization.optuna_trials_adx
            else:
                n_trials = 50  # Default
            
            # Split data for in-sample optimization
            split_point = len(main_df) - int(config.data.validation_days * 24 * 4)  # Convert days to 15min bars
            
            is_main_df = main_df.iloc[:split_point].copy()
            is_mta_df = mta_df.iloc[:split_point].copy() if not mta_df.empty else pd.DataFrame()
            
            if len(is_main_df) < 100:
                logger.warning(f"Insufficient in-sample data for {symbol} {strategy_mode}")
                return None
            
            # Run optimization
            best_params, opt_results = self.optimizer.optimize_strategy(
                df=is_main_df,
                mta_df=is_mta_df,
                strategy_mode=strategy_mode,
                n_trials=n_trials,
                timeout=config.optimization.optuna_timeout_per_phase,
                scoring_method=config.output.final_scoring_method.lower()
            )
            
            logger.info(f"{symbol} {strategy_mode}: Best score = {opt_results['best_score']:.3f}")
            
            return {
                'symbol': symbol,
                'strategy_mode': strategy_mode,
                'best_params': best_params,
                'optimization_results': opt_results,
                'is_data_points': len(is_main_df)
            }
            
        except Exception as e:
            logger.error(f"Error optimizing {symbol} {strategy_mode}: {e}")
            return None
    
    async def run_walk_forward_validation(
        self,
        symbol: str,
        strategy_mode: str,
        main_df: pd.DataFrame,
        mta_df: pd.DataFrame
    ) -> Optional[Dict[str, Any]]:
        """
        Run walk-forward validation for a strategy.
        
        Args:
            symbol: Trading symbol
            strategy_mode: Strategy mode
            main_df: Main timeframe data
            mta_df: MTA timeframe data
            
        Returns:
            Walk-forward results or None if failed
        """
        logger.info(f"Running walk-forward validation for {symbol} {strategy_mode}")
        
        try:
            # Adaptive walk-forward parameters based on available data
            total_days = len(main_df) / (24 * 4)  # Convert 15min bars to days
            logger.info(f"Total data available: {total_days:.1f} days ({len(main_df)} bars)")
            
            # Adaptive parameter selection based on available data
            if total_days >= 180:
                # Sufficient data for robust multiple windows (6+ months)
                in_sample_days = max(45, int(total_days * 0.25))  # 25% for training (45+ days)
                out_of_sample_days = max(15, int(total_days * 0.08))  # 8% for testing (15+ days)
                step_days = max(7, int(out_of_sample_days * 0.5))  # 50% of test period (weekly steps)
            elif total_days >= 90:
                # Moderate data - good windows (3+ months)
                in_sample_days = max(30, int(total_days * 0.35))  # 35% for training
                out_of_sample_days = max(10, int(total_days * 0.12))  # 12% for testing
                step_days = max(5, int(out_of_sample_days * 0.5))  # 50% of test period
            elif total_days >= 60:
                # Limited but usable data
                in_sample_days = max(20, int(total_days * 0.4))  # 40% for training
                out_of_sample_days = max(7, int(total_days * 0.15))  # 15% for testing
                step_days = max(3, int(out_of_sample_days * 0.6))  # 60% of test period
            elif total_days >= 30:
                # Minimal acceptable data
                in_sample_days = max(10, int(total_days * 0.4))  # 40% for training
                out_of_sample_days = max(3, int(total_days * 0.15))  # 15% for testing
                step_days = max(2, int(out_of_sample_days * 0.5))  # 50% of test period
            else:
                # Very limited data - not recommended for production
                in_sample_days = max(7, int(total_days * 0.5))  # 50% for training
                out_of_sample_days = max(2, int(total_days * 0.2))  # 20% for testing
                step_days = max(1, int(out_of_sample_days * 0.3))  # 30% of test period
            
            # Ensure we have enough data for at least one complete window
            min_required_days = in_sample_days + out_of_sample_days
            if total_days < min_required_days:
                logger.warning(f"Insufficient data for walk-forward: {total_days:.1f} days < {min_required_days} required")
                # Fall back to single validation split
                in_sample_days = max(7, int(total_days * 0.7))
                out_of_sample_days = int(total_days - in_sample_days)
                step_days = max(1, out_of_sample_days)
            
            logger.info(f"Walk-forward parameters: IS={in_sample_days}d, OOS={out_of_sample_days}d, step={step_days}d")
            
            wf_results = self.walk_forward_analyzer.run_walk_forward_analysis(
                df=main_df,
                mta_df=mta_df,
                strategy_mode=strategy_mode,
                in_sample_days=in_sample_days,
                out_of_sample_days=out_of_sample_days,
                step_days=step_days,
                n_trials_per_window=8  # Reduced from 15 to prevent overfitting in walk-forward
            )
            
            # Enhanced fallback for strategies with no walk-forward windows
            if wf_results['valid_windows'] == 0:
                logger.warning(f"No valid walk-forward windows for {symbol} {strategy_mode}")
                logger.info("Attempting single-split validation as fallback...")
                
                # Try single validation split approach
                try:
                    split_point = max(len(main_df) // 2, len(main_df) - 2000)  # Use later half or last 2000 bars
                    validation_df = main_df.iloc[split_point:].copy()
                    validation_mta_df = mta_df.iloc[split_point:].copy() if not mta_df.empty else pd.DataFrame()
                    
                    if len(validation_df) >= 100:  # Minimum for meaningful validation
                        # Run single validation
                        single_wf_results = self.walk_forward_analyzer.run_walk_forward_analysis(
                            df=validation_df,
                            mta_df=validation_mta_df,
                            strategy_mode=strategy_mode,
                            in_sample_days=max(5, len(validation_df) // (24 * 4 * 2)),  # Half the validation data
                            out_of_sample_days=max(2, len(validation_df) // (24 * 4 * 4)),  # Quarter validation data
                            step_days=max(1, len(validation_df) // (24 * 4 * 8)),  # Eighth validation data
                            n_trials_per_window=10
                        )
                        
                        if single_wf_results['valid_windows'] > 0:
                            logger.info(f"Single-split validation successful with {single_wf_results['valid_windows']} window")
                            wf_results = single_wf_results
                        else:
                            logger.warning("Single-split validation also failed")
                    else:
                        logger.warning(f"Insufficient validation data: {len(validation_df)} bars")
                        
                except Exception as e:
                    logger.error(f"Single-split validation failed: {e}")
            
            # Final fallback - use optimization results with conservative assumptions
            if wf_results['valid_windows'] == 0:
                logger.warning(f"All validation attempts failed for {symbol} {strategy_mode}, using optimization results only")
                wf_results = {
                    'valid_windows': 1,
                    'walk_forward_score': 3.0,  # Conservative default score
                    'aggregate_metrics': {
                        'avg_oos_sortino': 0.8,  # Conservative assumption
                        'cumulative_oos_return': 0.02  # 2% conservative return assumption
                    }
                }
            
            logger.info(f"{symbol} {strategy_mode}: {wf_results['valid_windows']} valid WF windows, "
                       f"WF score = {wf_results['walk_forward_score']:.3f}")
            
            return {
                'symbol': symbol,
                'strategy_mode': strategy_mode,
                'walk_forward_results': wf_results
            }
            
        except Exception as e:
            logger.error(f"Error in walk-forward validation for {symbol} {strategy_mode}: {e}")
            return None
    
    def validate_and_score_strategies(
        self,
        optimization_results: list,
        walk_forward_results: list
    ) -> Dict[str, Dict[str, Any]]:
        """
        Validate and score all optimized strategies with rigorous anti-overfitting criteria.
        
        Args:
            optimization_results: List of optimization results
            walk_forward_results: List of walk-forward results
            
        Returns:
            Dictionary of validated strategies with comprehensive scores
        """
        logger.info("Validating and scoring strategies with anti-overfitting criteria")
        
        validated_strategies = {}
        
        # Combine optimization and walk-forward results
        for opt_result in optimization_results:
            if opt_result is None:
                continue
            
            symbol = opt_result['symbol']
            strategy_mode = opt_result['strategy_mode']
            strategy_key = f"{symbol}_{strategy_mode}"
            
            # Find corresponding walk-forward results
            wf_result = None
            for wf in walk_forward_results:
                if (wf and wf['symbol'] == symbol and 
                    wf['strategy_mode'] == strategy_mode):
                    wf_result = wf['walk_forward_results']
                    break
            
            # DETAILED PERFORMANCE ANALYSIS (Windows-safe logging)
            print(f"\n===== DETAILED ANALYSIS FOR {strategy_key} =====")
            
            # Optimization results detail
            opt_metrics = opt_result.get('optimization_results', {}).get('final_metrics', {})
            best_score = opt_result.get('best_score', 0.0)
            best_params = opt_result.get('best_params', {})
            
            print(f"OPTIMIZATION RESULTS:")
            print(f"   Best Score: {best_score:.3f}")
            print(f"   Total Return: {opt_metrics.get('total_return', 0)*100:.2f}%")
            print(f"   Sortino Ratio: {opt_metrics.get('sortino_ratio', 0):.2f}")
            print(f"   Profit Factor: {opt_metrics.get('profit_factor', 0):.2f}")
            print(f"   Win Rate: {opt_metrics.get('win_rate', 0)*100:.1f}%")
            print(f"   Max Drawdown: {opt_metrics.get('max_drawdown', 0)*100:.2f}%")
            print(f"   Trade Count: {opt_metrics.get('trade_count', 0)}")
            print(f"   Avg Win: ${opt_metrics.get('avg_win', 0):.2f}")
            print(f"   Avg Loss: ${opt_metrics.get('avg_loss', 0):.2f}")
            
            # Best parameters detail
            print(f"\nBEST PARAMETERS:")
            for key, value in best_params.items():
                if isinstance(value, float):
                    print(f"   {key}: {value:.3f}")
                else:
                    print(f"   {key}: {value}")
            
            # Walk-forward analysis detail
            if wf_result and wf_result.get('valid_windows', 0) > 0:
                print(f"\nWALK-FORWARD ANALYSIS:")
                print(f"   Valid Windows: {wf_result['valid_windows']}")
                print(f"   WF Score: {wf_result['walk_forward_score']:.3f}")
                
                wf_metrics = wf_result.get('aggregate_metrics', {})
                print(f"   OOS Return: {wf_metrics.get('cumulative_oos_return', 0)*100:.2f}%")
                print(f"   OOS Sortino: {wf_metrics.get('avg_oos_sortino', 0):.2f}")
                print(f"   Sortino Stability: {wf_metrics.get('sortino_stability', 0):.3f}")
                
                # Window-by-window breakdown
                if 'window_results' in wf_result:
                    print(f"   Window Breakdown:")
                    for i, window in enumerate(wf_result['window_results']):
                        print(f"     Window {i+1}: IS_Sortino={window.get('is_sortino', 0):.1f}, "
                                  f"OOS_Sortino={window.get('oos_sortino', 0):.1f}, "
                                  f"Trades={window.get('oos_trades', 0)}")
            
            # RELAXED VALIDATION: Focus on getting any working strategy
            if wf_result is None or wf_result['valid_windows'] < config.optimization.min_walk_forward_windows:
                # If no walk-forward data, allow optimization results if they show promise
                final_metrics = opt_result['optimization_results']['final_metrics']
                if final_metrics.get('total_return', -1) > 0.001:  # Accept any positive return > 0.1%
                    logger.info(f"{strategy_key}: Accepted despite insufficient walk-forward (positive optimization return)")
                    wf_result = {
                        'valid_windows': 1,
                        'walk_forward_score': 2.0,
                        'aggregate_metrics': {
                            'cumulative_oos_return': final_metrics.get('total_return', 0.01),
                            'avg_oos_sortino': max(0.1, final_metrics.get('sortino_ratio', 0.1))
                        }
                    }
                else:
                    logger.warning(f"{strategy_key}: Insufficient walk-forward windows AND poor optimization return")
                    continue
            
            # Extract key metrics
            final_metrics = opt_result['optimization_results']['final_metrics']
            wf_metrics = wf_result['aggregate_metrics']
            
            # ANTI-OVERFITTING VALIDATION: Check stability between IS and OOS
            is_sortino = final_metrics['sortino_ratio']
            oos_sortino = wf_metrics.get('avg_oos_sortino', 0)
            is_return = final_metrics['total_return']
            oos_return = wf_metrics.get('cumulative_oos_return', 0)
            
            # Calculate stability ratios
            sortino_stability = (oos_sortino / is_sortino) if is_sortino > 0 else 0
            return_stability = (oos_return / is_return) if is_return > 0 else 0
            
            # ROBUSTNESS CHECKS
            robustness_checks = {
                'min_trades': final_metrics['trade_count'] >= config.optimization.min_trades,
                'min_sortino': final_metrics['sortino_ratio'] >= config.optimization.min_sortino_ratio,
                'min_profit_factor': final_metrics['profit_factor'] >= config.optimization.min_profit_factor,
                'max_drawdown': final_metrics['max_drawdown'] <= config.optimization.max_drawdown,
                'min_win_rate': final_metrics['win_rate'] >= config.optimization.min_win_rate,
                'sortino_stability': sortino_stability >= config.optimization.stability_threshold,
                'oos_positive_return': oos_return > 0,  # OOS must be profitable
                'oos_min_sortino': oos_sortino >= config.optimization.min_sortino_ratio * 0.5  # OOS Sortino at least 50% of requirement
            }
            
            # Count successful checks
            passed_checks = sum(robustness_checks.values())
            total_checks = len(robustness_checks)
            robustness_score = passed_checks / total_checks
            
            # STRICT ACCEPTANCE CRITERIA
            critical_checks = ['min_trades', 'oos_positive_return', 'sortino_stability']
            critical_passed = all(robustness_checks[check] for check in critical_checks)
            
            if not critical_passed or robustness_score < 0.75:  # Must pass 75% of all checks
                failed_checks = [check for check, passed in robustness_checks.items() if not passed]
                logger.info(f"{strategy_key} failed robustness validation. Failed checks: {failed_checks}")
                logger.info(f"  Robustness score: {robustness_score:.2f}, Sortino stability: {sortino_stability:.2f}")
                continue
            
            # CONSISTENCY CHECK: Verify walk-forward windows consistency
            individual_windows = wf_result.get('individual_windows', [])
            if individual_windows:
                profitable_windows = sum(1 for w in individual_windows 
                                       if w.get('oos_metrics', {}).get('total_return', 0) > 0)
                consistency_ratio = profitable_windows / len(individual_windows)
                
                if consistency_ratio < config.optimization.consistency_threshold:
                    logger.info(f"{strategy_key} failed consistency check: {consistency_ratio:.2f} < {config.optimization.consistency_threshold}")
                    continue
            
            # Calculate comprehensive score with new weights
            quantum_score = final_metrics['quantum_score']
            walk_forward_score = wf_result['walk_forward_score']
            
            comprehensive_score = (
                quantum_score * config.optimization.quantum_score_weight +
                walk_forward_score * config.optimization.walk_forward_weight +
                robustness_score * 10 * config.optimization.robustness_weight  # Scale robustness to 0-10
            )
            
            # Store validated strategy
            validated_strategies[strategy_key] = {
                'symbol': symbol,
                'strategy_mode': strategy_mode,
                'best_params': opt_result['best_params'],
                'optimization_metrics': final_metrics,
                'walk_forward_metrics': wf_result['aggregate_metrics'],
                'comprehensive_score': comprehensive_score,
                'component_scores': {
                    'quantum_score': quantum_score,
                    'walk_forward_score': walk_forward_score,
                    'robustness_score': robustness_score,
                    'sortino_stability': sortino_stability,
                    'return_stability': return_stability,
                    'consistency_ratio': consistency_ratio if 'consistency_ratio' in locals() else 0
                },
                'robustness_checks': robustness_checks,
                'validation_reason': f'Passed rigorous validation (robustness: {robustness_score:.2f})'
            }
            
            logger.info(f"{strategy_key} validated - Comprehensive Score: {comprehensive_score:.2f}")
            logger.info(f"  Robustness: {robustness_score:.2f}, Stability: {sortino_stability:.2f}, OOS Return: {oos_return:.3f}")
        
        return validated_strategies
    
    def save_results(self, validated_strategies: Dict[str, Dict[str, Any]]):
        """
        Save optimization results to files.
        
        Args:
            validated_strategies: Dictionary of validated strategies
        """
        logger.info("Saving optimization results")
        
        if not validated_strategies:
            logger.warning("No validated strategies to save")
            return
        
        # Rank strategies
        ranked_strategies = ScoringSystem.rank_strategies(
            validated_strategies, 
            ranking_method=config.output.final_scoring_method.lower()
        )
        
        # Prepare strategies for saving (top N)
        strategies_to_save = {}
        for i, (strategy_key, strategy_data) in enumerate(ranked_strategies[:config.output.max_strategies_to_save]):
            strategies_to_save[strategy_key] = {
                'rank': i + 1,
                'symbol': strategy_data['symbol'],
                'strategy_mode': strategy_data['strategy_mode'],
                'parameters': strategy_data['best_params'],
                'performance': {
                    'comprehensive_score': strategy_data['comprehensive_score'],
                    'quantum_score': strategy_data['component_scores']['quantum_score'],
                    'sortino_ratio': strategy_data['optimization_metrics']['sortino_ratio'],
                    'total_return': strategy_data['optimization_metrics']['total_return'],
                    'max_drawdown': strategy_data['optimization_metrics']['max_drawdown'],
                    'win_rate': strategy_data['optimization_metrics']['win_rate'],
                    'trade_count': strategy_data['optimization_metrics']['trade_count'],
                    'walk_forward_score': strategy_data['component_scores']['walk_forward_score']
                },
                'validation_timestamp': datetime.now().isoformat()
            }
        
        # Save to JSON file
        output_file = 'optimized_strategies.json'
        with open(output_file, 'w') as f:
            json.dump(strategies_to_save, f, indent=4, default=str)
        
        logger.info(f"Saved {len(strategies_to_save)} strategies to {output_file}")
        
        # Print summary
        self.print_results_summary(ranked_strategies[:config.output.max_strategies_to_save])
    
    def print_results_summary(self, ranked_strategies: list):
        """Print a summary of the optimization results."""
        print("\n" + "="*80)
        print("ULTRATRADER OPTIMIZATION RESULTS SUMMARY")
        print("="*80)
        
        for i, (strategy_key, strategy_data) in enumerate(ranked_strategies, 1):
            print(f"\n{i}. {strategy_key}")
            print(f"   Strategy Mode: {strategy_data['strategy_mode']}")
            print(f"   Comprehensive Score: {strategy_data['comprehensive_score']:.2f}")
            print(f"   Quantum Score: {strategy_data['component_scores']['quantum_score']:.2f}")
            print(f"   Sortino Ratio: {strategy_data['optimization_metrics']['sortino_ratio']:.2f}")
            print(f"   Total Return: {strategy_data['optimization_metrics']['total_return']*100:.1f}%")
            print(f"   Max Drawdown: {strategy_data['optimization_metrics']['max_drawdown']*100:.1f}%")
            print(f"   Win Rate: {strategy_data['optimization_metrics']['win_rate']*100:.1f}%")
            print(f"   Trade Count: {strategy_data['optimization_metrics']['trade_count']}")
            print(f"   Walk-Forward Score: {strategy_data['component_scores']['walk_forward_score']:.2f}")
    
    async def run_optimization_pipeline(self):
        """Run the complete optimization pipeline."""
        logger.info("Starting Ultratrader optimization pipeline")
        
        try:
            # Initialize Binance client
            await self.initialize_binance_client()
            
            # Determine symbols to optimize
            symbols_to_optimize = []
            if config.market.target_symbol:
                symbols_to_optimize = [config.market.target_symbol]
            else:
                # TODO: Add logic to fetch top symbols by volume
                symbols_to_optimize = ['BTCUSDT']  # Default for now
            
            logger.info(f"Optimizing strategies for symbols: {symbols_to_optimize}")
            
            all_optimization_results = []
            all_walk_forward_results = []
            
            # Process each symbol
            for symbol in symbols_to_optimize:
                try:
                    # Get market data
                    main_df, mta_df = await self.get_market_data(symbol)
                    
                    # Strategy modes to test
                    strategy_modes = ['Trend', 'Reversion', 'Adaptive']
                    
                    # Optimize each strategy mode
                    for strategy_mode in strategy_modes:
                        # Run optimization
                        opt_result = await self.optimize_single_strategy(
                            symbol, strategy_mode, main_df, mta_df
                        )
                        if opt_result:
                            all_optimization_results.append(opt_result)
                        
                        # Run walk-forward validation
                        wf_result = await self.run_walk_forward_validation(
                            symbol, strategy_mode, main_df, mta_df
                        )
                        if wf_result:
                            all_walk_forward_results.append(wf_result)
                
                except Exception as e:
                    logger.error(f"Error processing symbol {symbol}: {e}")
                    continue
            
            # Validate and score strategies
            validated_strategies = self.validate_and_score_strategies(
                all_optimization_results, all_walk_forward_results
            )
            
            # Save results
            self.save_results(validated_strategies)
            
            if validated_strategies:
                logger.info(f"Optimization completed successfully with {len(validated_strategies)} validated strategies")
            else:
                logger.warning("Optimization completed but no strategies passed validation")
        
        except Exception as e:
            logger.error(f"Critical error in optimization pipeline: {e}")
            traceback.print_exc()
            raise
        
        finally:
            if self.client:
                await self.client.close_connection()
                logger.info("Binance client connection closed")


async def main():
    """Main entry point."""
    print("🚀 Ultratrader Modular Optimization System")
    print("=" * 50)
    
    logger.info("Ultratrader Orchestrator initialized")
    orchestrator = UltraderOrchestrator()
    
    try:
        await orchestrator.run_optimization_pipeline()
        print("\n✅ Optimization pipeline completed successfully!")
    
    except KeyboardInterrupt:
        logger.warning("Process interrupted by user")
        print("\n⚠️  Process interrupted by user")
    
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n❌ Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    try:
        # Validate configuration before starting
        config.validate()
        print("✅ Configuration validated successfully")
        
        # Run the main optimization pipeline
        asyncio.run(main())
        
    except Exception as e:
        print(f"❌ Startup error: {e}")
        logger.error(f"Startup error: {e}")
        sys.exit(1) 